<template>
  <div class="app-container basis-detail-page">
    <template v-if="formState.opeation === 'edit'">
      <div class="detail-title">
        <span class="bfb55 tl">修改前商品信息</span>
        <span class="bfb45 tl ml10" style="border-left: 3px solid #004b5c;padding-left: 7px;">修改后商品信息</span>
      </div>
      <div class="detail-table">
        <table>
          <tr>
            <th>商品名称</th>
            <td>{{ oldDetailState.itemName }}</td>
            <td>{{ newDetailState.itemName }}</td>
          </tr>
          <tr>
            <th>商品类目名称</th>
            <td>{{ oldDetailState.categoryNames }}</td>
            <td>{{ newDetailState.categoryNames }}</td>
          </tr>
          <tr>
            <th>商品副标题</th>
            <td>{{ oldDetailState.titleName }}</td>
            <td>{{ newDetailState.titleName }}</td>
          </tr>
          <tr>
            <th>通用名称</th>
            <td>{{ oldDetailState.universalName }}</td>
            <td>{{ newDetailState.universalName }}</td>
          </tr>
          <tr>
            <th>税收商品分类编码</th>
            <td>{{ oldDetailState.taxCode }}</td>
            <td>{{ newDetailState.taxCode }}</td>
          </tr>
          <tr>
            <th>税收商品分类名称</th>
            <td>{{ oldDetailState.taxName }}</td>
            <td>{{ newDetailState.taxName }}</td>
          </tr>
          <tr>
            <th>商品图片</th>
            <td>
              <template v-if="oldDetailState.images">
                <ImagePreview
                  v-for="item in oldDetailState.images"
                  :model-value="item"
                  :width="80"
                  :height="80"
                  class="left mr8 mb8"
                />
              </template>
            </td>
            <td>
              <template v-if="newDetailState.images">
                <ImagePreview
                  v-for="item in newDetailState.images"
                  :model-value="item"
                  :width="80"
                  :height="80"
                  class="left mr8 mb8"
                />
              </template>
            </td>
          </tr>
          <tr>
            <th>品牌</th>
            <td>{{ oldDetailState.brandName }}</td>
            <td>{{ newDetailState.brandName }}</td>
          </tr>
          <tr>
            <th>商品类型</th>
            <td>
              <dict-tag :options="sys_oms_prj_item_data_type" :value="oldDetailState.dataType" />
            </td>
            <td>
              <dict-tag :options="sys_oms_prj_item_data_type" :value="newDetailState.dataType" />
            </td>
          </tr>
          <tr>
            <th>包装清单</th>
            <td>{{ oldDetailState.packing }}</td>
            <td>{{ newDetailState.packing }}</td>
          </tr>
          <tr>
            <th>税率</th>
            <td>{{ oldDetailState.vatrate }}</td>
            <td>{{ newDetailState.vatrate }}</td>
          </tr>
          <tr>
            <th>单位</th>
            <td>{{ oldDetailState.unitName }}</td>
            <td>{{ newDetailState.unitName }}</td>
          </tr>
          <tr>
            <th>客户专用描述</th>
            <td>{{ oldDetailState.csrDesc }}</td>
            <td>{{ newDetailState.csrDesc }}</td>
          </tr>
          <tr>
            <th>京东链接</th>
            <td>{{ oldDetailState.comparePriceJson[0] }}</td>
            <td>{{ newDetailState.comparePriceJson[0] }}</td>
          </tr>
          <tr>
            <th>天猫链接</th>
            <td>{{ oldDetailState.comparePriceJson[1] }}</td>
            <td>{{ newDetailState.comparePriceJson[1] }}</td>
          </tr>
          <tr>
            <th>苏宁链接</th>
            <td>{{ oldDetailState.comparePriceJson[2] }}</td>
            <td>{{ newDetailState.comparePriceJson[2] }}</td>
          </tr>
          <tr>
            <th>普通属性</th>
            <td>
              <template v-if="oldDetailState.commonAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in oldDetailState.commonAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
            <td>
              <template v-if="newDetailState.commonAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in newDetailState.commonAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
          </tr>
          <tr>
            <th>自定义属性</th>
            <td>
              <template v-if="oldDetailState.customAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in oldDetailState.customAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
            <td>
              <template v-if="newDetailState.customAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in newDetailState.customAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
          </tr>
        </table>
      </div>
      <div class="detail-title">SKU信息</div>
      <div class="detail-table">
        <table>
          <tr>
            <th>skuId</th>
            <td>{{ oldDetailState.skuVo['id'] }}</td>
            <td>{{ newDetailState.skuVo['id'] }}</td>
          </tr>
          <tr>
            <th>sku编码</th>
            <td>{{ oldDetailState.skuVo['skuCode'] }}</td>
            <td>{{ newDetailState.skuVo['skuCode'] }}</td>
          </tr>
          <tr>
            <th>规格</th>
            <td>{{ oldDetailState.skuVo['specification'] }}</td>
            <td>{{ newDetailState.skuVo['specification'] }}</td>
          </tr>
          <tr>
            <th>型号</th>
            <td>{{ oldDetailState.skuVo['communityModel'] }}</td>
            <td>{{ newDetailState.skuVo['communityModel'] }}</td>
          </tr>
          <tr>
            <th>条形码</th>
            <td>{{ oldDetailState.skuVo['barcode'] }}</td>
            <td>{{ newDetailState.skuVo['barcode'] }}</td>
          </tr>
          <tr>
            <th>颜色</th>
            <td>{{ oldDetailState.itemColor }}</td>
            <td>{{ newDetailState.itemColor }}</td>
          </tr>
          <tr>
            <th>市场价</th>
            <td>
              <MoneyTag v-if="oldDetailState.skuVo['marketPrice']" :value="oldDetailState.skuVo['marketPrice']" />
            </td>
            <td>
              <MoneyTag v-if="newDetailState.skuVo['marketPrice']" :value="newDetailState.skuVo['marketPrice']" />
            </td>
          </tr>
          <tr>
            <th>项目售价</th>
            <td>
              <MoneyTag v-if="oldDetailState.skuVo['salePrice']" :value="oldDetailState.skuVo['salePrice']" />
            </td>
            <td>
              <MoneyTag v-if="newDetailState.skuVo['salePrice']" :value="newDetailState.skuVo['salePrice']" />
            </td>
          </tr>
        </table>
      </div>
      <div class="detail-title">商品详情</div>
      <div class="detail-table">
        <table>
          <tr>
            <th>PC详情</th>
            <td>
              <template v-for="(item, idx) in oldDetailState.goodsItemDetailVo.pcDetail">
                <p>{{ item.title }}</p>
                <div v-html="item.content || ''" />
              </template>
            </td>
            <td>
              <template v-for="(item, idx) in newDetailState.goodsItemDetailVo.pcDetail">
                <p>{{ item.title }}</p>
                <div v-html="item.content || ''" />
              </template>
            </td>
          </tr>
          <tr>
            <th>WAP详情</th>
            <td>
              <template v-for="(item, idx) in oldDetailState.goodsItemDetailVo.wapDetail">
                <p>{{ item.title }}</p>
                <div v-html="item.content || ''" />
              </template>
            </td>
            <td>
              <template v-for="(item, idx) in newDetailState.goodsItemDetailVo.wapDetail">
                <p>{{ item.title }}</p>
                <div v-html="item.content || ''" />
              </template>
            </td>
          </tr>
        </table>
      </div>
    </template>
    <template v-else>
      <div class="detail-title">基本信息</div>
      <div class="detail-table">
        <table>
          <tr>
            <th>商品名称</th>
            <td>{{ oldDetailState.itemName }}</td>
            <th>商品类目名称</th>
            <td>{{ oldDetailState.categoryNames }}</td>
          </tr>
          <tr>
            <th>商品副标题</th>
            <td>{{ oldDetailState.titleName }}</td>
            <th>通用名称</th>
            <td>{{ oldDetailState.universalName }}</td>
          </tr>
          <tr>
            <th>税收商品分类编码</th>
            <td>{{ oldDetailState.taxCode }}</td>
            <th>税收商品分类名称</th>
            <td>{{ oldDetailState.taxName }}</td>
          </tr>
          <tr>
            <th rowspan="3">商品图片</th>
            <td rowspan="3">
              <template v-if="oldDetailState.images">
                <ImagePreview
                  v-for="item in oldDetailState.images"
                  :model-value="item"
                  :width="80"
                  :height="80"
                  class="left mr8 mb8"
                />
              </template>
            </td>
            <th>品牌</th>
            <td>{{ oldDetailState.brandName }}</td>
          </tr>
          <tr>
            <th>商品类型</th>
            <td>
              <dict-tag :options="sys_oms_prj_item_data_type" :value="oldDetailState.dataType" />
            </td>
          </tr>
          <tr>
            <th>包装清单</th>
            <td>{{ oldDetailState.packing }}</td>
          </tr>
          <tr>
            <th>税率</th>
            <td>{{ oldDetailState.vatrate }}</td>
            <th>单位</th>
            <td>{{ oldDetailState.unitName }}</td>
          </tr>
          <tr>
            <th>客户专用描述</th>
            <td>{{ oldDetailState.csrDesc }}</td>
            <th>京东链接</th>
            <td>{{ oldDetailState.comparePriceJson[0] }}</td>
          </tr>
          <tr>
            <th>天猫链接</th>
            <td>{{ oldDetailState.comparePriceJson[1] }}</td>
            <th>苏宁链接</th>
            <td>{{ oldDetailState.comparePriceJson[2] }}</td>
          </tr>
          <tr>
            <th>普通属性</th>
            <td>
              <template v-if="oldDetailState.commonAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in oldDetailState.commonAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
            <th>自定义属性</th>
            <td>
              <template v-if="oldDetailState.customAttr.length">
                <el-row style="border-bottom: 1px dashed #ddd;margin-bottom: 8px;padding-bottom: 8px;">
                  <el-col :span="12">属性名称</el-col>
                  <el-col :span="12">属性值</el-col>
                </el-row>
                <el-row>
                  <template v-for="item in oldDetailState.customAttr">
                    <el-col :span="12">{{ item.attrkey || '--' }}</el-col>
                    <el-col :span="12">{{ item.attrVal || '--' }}</el-col>
                  </template>
                </el-row>
              </template>
            </td>
          </tr>
        </table>
      </div>
      <div class="detail-title">SKU信息</div>
      <div class="basis-table">
        <TableFilter
          ref="tableFilter"
          row-key="id"
          height="100%"
          :no-pagination="true"
          :column="skuTableColumn"
          :data="oldDetailState.skuVo ? [oldDetailState.skuVo] : []"
        >
          <template #itemColorSlot="{ row }">
            {{ oldDetailState.itemColor }}
          </template>
          <template #marketPriceSlot="{ row }">
            <MoneyTag :value="row.marketPrice" />
          </template>
          <template #salePriceSlot="{ row }">
            <MoneyTag :value="row.salePrice" />
          </template>
        </TableFilter>
      </div>
      <div class="detail-title">商品详情</div>
      <el-tabs v-model="oldDetailState.tabActive" class="common-tabs-2 bfb">
        <el-tab-pane label="PC详情" name="PC" />
        <el-tab-pane label="WAP详情" name="WAP" />
      </el-tabs>
      <div class="min-height500 over-auto p16">
        <div v-if="oldDetailState.tabActive === 'PC'" class="height-all">
          <template v-for="(item, idx) in oldDetailState.goodsItemDetailVo.pcDetail">
            <p>{{ item.title }}</p>
            <div v-html="item.content || ''" />
          </template>
        </div>
        <div v-else-if="oldDetailState.tabActive === 'WAP'" class="height-all">
          <template v-for="(item, idx) in oldDetailState.goodsItemDetailVo.wapDetail">
            <p>{{ item.title }}</p>
            <div v-html="item.content || ''" />
          </template>
        </div>
      </div>
    </template>
    <el-form :model="formState" :rules="rulesState" ref="ruleFormRef" label-width="100px">
      <el-form-item label="审核" prop="status">
        <el-radio-group v-model="formState.status" @change="ruleFormRef.clearValidate(['reason'])">
          <el-radio :label="2" :value="2">通过</el-radio>
          <el-radio :label="3" :value="3">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="reason" :required="formState.status === 3">
        <el-input type="textarea" :rows="4" v-model="formState.reason" placeholder="请输入审核意见" />
      </el-form-item>
    </el-form>
    <div class="basis-form-button">
      <el-button @click="onCancel">返回</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </div>
  </div>
  <!-- 发布官网项目弹窗 -->
  <PushGwDialog v-if="pushGwDialog.visible" v-model="pushGwDialog" />
</template>
<script name="MdmGoodsManageAuditDetail" setup>
import useDict from '@/utils/dict'
import useTab from '@/utils/tab'
import ImagePreview from '@/components/ImagePreview/index.vue'
import TableFilter from '@/components/TableFilter/index.vue'
import MoneyTag from '@/components/MoneyTag/index.vue'
import PushGwDialog from './components/PushGwDialog.vue'
import useModal from '@/utils/modal'
import { getItemAudit, submitItemAudit } from '@/api/mdm/itemAudit'

const route = useRoute()
const router = useRouter()

// 枚举字典
const { sys_oms_prj_item_data_type } = useDict('sys_oms_prj_item_data_type')
// data
// 页面加载状态
const loading = ref(false)
const ruleFormRef = ref(null)
const oldDetailState = reactive({
  goodsSkuVoList: [],
  goodsItemDetailVo: {
    pcDetail: [],
    wapDetail: []
  },
  commonAttr: [],
  customAttr: [],
  comparePriceJson: [],
  skuVo: {},
  tabActive: 'PC'
})
const newDetailState = reactive({
  goodsSkuVoList: [],
  goodsItemDetailVo: {
    pcDetail: [],
    wapDetail: []
  },
  commonAttr: [],
  customAttr: [],
  comparePriceJson: [],
  skuVo: {},
  tabActive: 'PC'
})
// sku信息
const skuTableColumn = ref([
  { label: 'skuId', prop: 'id', minWidth: '140px' },
  { label: 'sku编码', prop: 'skuCode', minWidth: '140px' },
  { label: '规格', prop: 'specification', minWidth: '140px' },
  { label: '型号', prop: 'communityModel', minWidth: '140px' },
  { label: '条形码', prop: 'barcode', minWidth: '140px' },
  { label: '颜色', slotName: 'itemColorSlot', minWidth: '140px' },
  { label: '市场价', slotName: 'marketPriceSlot', minWidth: '140px' },
  { label: '项目售价', slotName: 'salePriceSlot', minWidth: '140px' }
])
// 表单数据
const formState = ref({
  prjId: undefined,
  itemId: undefined,
  skuId: undefined,
  status: undefined,
  reason: undefined,
  opeation: undefined
})
const rulesState = reactive({
  status: [{ required: true, message: '请选择审核' }],
  reason: [{ required: true, message: '请输入审核意见' }]
})
// 发布官网项目弹窗
const pushGwDialog = ref({ form: {}, visible: false })

onMounted(() => {
  // 加载数据
  if (route.query.id) {
    loadData(route.query.id)
  }
})

// methods
const onSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (formState.value.opeation === 'add' && +formState.value.status === 2) {
        useModal.confirm('是否发布官网项目', '提示').then(() => {
          Object.assign(pushGwDialog.value, {
            form: {
              ...formState.value,
              pushGw: 'Y',
              officialSalePrice: oldDetailState.skuVo[0] && oldDetailState.skuVo[0].salePrice || undefined,
              officialMarketPrice: oldDetailState.skuVo[0] && oldDetailState.skuVo[0].marketPrice || undefined,
              id: route.query.id
            },
            visible: true
          })
        }).catch(() => {
          submitItemAudit({
            ...formState.value,
            pushGw: 'N',
            id: route.query.id
          }).then(() => {
            useModal.msgSuccess('操作成功')
            useTab(router).removeCache('MdmGoodsManageAudit')
            onCancel()
          })
        })
      } else {
        submitItemAudit({
          ...formState.value,
          id: route.query.id,
          pushGw: undefined
        }).then(() => {
          useModal.msgSuccess('操作成功')
          useTab(router).removeCache('MdmGoodsManageAudit')
          onCancel()
        })
      }
    }
  })
}
const onCancel = () => {
  useTab(router).closeOpenPage('/mdm/goods/goods-manage-audit')
}
// 回显加载数据
const loadData = (id) => {
  loading.value = true
  getItemAudit(id).then((res) => {
    const oldItemVo = res.data.oldItemVo || {}
    const oldItemDetailVo = res.data.oldItemDetailVo || {}
    const oldComparePriceJson = JSON.parse(oldItemVo.comparePriceJson || '[]')
    const oldAttrJson = JSON.parse(oldItemVo.attrJson || '{}')
    const oldPcDetail = oldItemDetailVo && JSON.parse(oldItemDetailVo.pcDetail || '[]') || []
    const oldWapDetail = oldItemDetailVo && JSON.parse(oldItemDetailVo.wapDetail || '[]') || []
    Object.assign(oldDetailState, {
      ...oldItemVo,
      comparePriceJson: {
        0: oldComparePriceJson[0],
        1: oldComparePriceJson[1],
        2: oldComparePriceJson[2]
      },
      supportReturn: oldItemVo.supportReturn,
      commonAttr: oldAttrJson.commonAttr || [],
      customAttr: oldAttrJson.customAttr || [],
      goodsItemDetailVo: { pcDetail: oldPcDetail, wapDetail: oldWapDetail },
      skuVo: res.data.oldSkuVo
    })

    const newItemVo = res.data.newItemVo || {}
    const newItemDetailVo = res.data.newItemDetailVo || {}
    const newComparePriceJson = JSON.parse(newItemVo.comparePriceJson || '[]')
    const newAttrJson = JSON.parse(newItemVo.attrJson || '{}')
    const newPcDetail = newItemDetailVo && JSON.parse(newItemDetailVo.pcDetail || '[]') || []
    const newWapDetail = newItemDetailVo && JSON.parse(newItemDetailVo.wapDetail || '[]') || []
    Object.assign(newDetailState, {
      ...newItemVo,
      comparePriceJson: {
        0: newComparePriceJson[0],
        1: newComparePriceJson[1],
        2: newComparePriceJson[2]
      },
      supportReturn: newItemVo.supportReturn,
      commonAttr: newAttrJson.commonAttr || [],
      customAttr: newAttrJson.customAttr || [],
      goodsItemDetailVo: { pcDetail: newPcDetail, wapDetail: newWapDetail },
      skuVo: res.data.newSkuVo
    })

    Object.assign(formState.value, {
      prjId: res.data.prjId,
      itemId: res.data.itemId,
      skuId: res.data.skuId,
      opeation: res.data.opeation
    })
    loading.value = false
  })
}
</script>
<style lang="scss" scoped>
:deep(img) {
  max-width: 100%;
}
</style>