<template>
  <el-dialog
    v-model="dialogVisible"
    width="1300px"
    title="选择销售单据"
    :close-on-click-modal="false"
    :before-close="onCancel"
  >
    <div class="common-filter-wrap">
      <el-form class="common-filter common-filter-clearfix">
        <el-form-item label="包裹单号" prop="packageOrderId">
          <el-input v-model="searchState.searchForm.packageOrderId" placeholder="请输入包裹单号" clearable />
        </el-form-item>
        <el-form-item label="销售订单号" prop="sellerOrderId">
          <el-input v-model="searchState.searchForm.sellerOrderId" placeholder="请输入销售订单号" clearable />
        </el-form-item>
        <el-form-item label="外部单号" prop="outId">
          <el-input v-model="searchState.searchForm.outId" placeholder="请输入外部单号" clearable />
        </el-form-item>
        <el-form-item label="项目" prop="prjId">
          <SelectWaterfall
            v-model="searchState.searchForm.prjId"
            :props="prjProps"
            placeholder="请选择项目"
            class="bfb"
          />
        </el-form-item>
        <el-form-item label="销售日期" prop="sellTime">
          <el-date-picker
            v-model="searchState.searchForm.sellTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="bfb"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
        <el-form-item label="发货日期" prop="packageDate">
          <el-date-picker
            v-model="searchState.searchForm.packageDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="bfb"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
        <el-form-item class="filter-btns">
          <el-button type="primary" @click="search">筛 选</el-button>
          <el-button @click="reset">重 置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="basis-table">
      <TableFilter
        ref="tableFilter"
        row-key="id"
        data-key="rows"
        height="100%"
        :loading="loading"
        :column="tableState.column"
        :data="tableState.data"
        @pagination-change="paginationChange"
        @selection-change="(val) => (tableState.selection = val)"
      >
        <template #sellerSlot="{ row }">
          <div><span>包裹单号：</span>{{ row.packageOrderId || '--' }}</div>
          <div><span>销售订单号：</span>{{ row.sellerOrderId || '--' }}</div>
        </template>
        <template #dateSlot="{ row }">
          <div><span>销售：</span>{{ row.sellTime || '--' }}</div>
          <div><span>发货：</span>{{ row.packageDate || '--' }}</div>
        </template>
        <template #amountSlot="{ row }">
          <MoneyTag :value="row.sellAmount" />
        </template>
      </TableFilter>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
// import
import useModal from '@/utils/modal'
import useSearch from '@/utils/search'
import TableFilter from '@/components/TableFilter'
import SelectWaterfall from '@/components/SelectWaterfall/index.vue'
import MoneyTag from '@/components/MoneyTag/index.vue'
import { listStlmtSellerOrderCsr } from '@/api/stlmt/invoice/csrInvoice'
import { listPrj } from '@/api/oms/prj/prj'
import { getSearchDate } from '@/utils/common'

const props = defineProps({
  modelValue: {
    type: Object,
    require: true,
    default() {
      return {
        prjId: '',
        csrCode: '',
        selected: [],
        visible: false
      }
    }
  }
})
const emit = defineEmits(['update:modelValue', 'submitCallBack'])

// 查询对象
const { searchState, paginationChange, search, searchQuery, reset } = useSearch({
  noCache: true,
  searchForm: {
    packageOrderId: undefined,
    sellerOrderId: undefined,
    outId: undefined,
    prjId: undefined,
    sellTime: [],
    packageDate: []
  },
  getData: async (val) => {
    loading.value = true
    selectionSave()
    const res = await listStlmtSellerOrderCsr({
      ...val,
      operatorId: props.modelValue.prjId,
      deliverCustomerCode: props.modelValue.csrCode,
      sellStartTime: getSearchDate(val.sellTime, 0),
      sellEndTime: getSearchDate(val.sellTime, 1),
      sellTime: undefined,
      packageStartDate: getSearchDate(val.packageDate, 0),
      packageEndDate: getSearchDate(val.packageDate, 1),
      packageDate: undefined,
      pageNum: searchState.pageNum,
      pageSize: searchState.pageSize
    })
    Object.assign(tableState.data, { pageNum: searchState.pageNum, pageSize: searchState.pageSize, ...res })
    await nextTick(() => {
      const authList = authObjList.value.map((v) => v.id)
      tableState.data['rows']
        .filter((v) => authList.indexOf(v.id) !== -1)
        .forEach((row) => {
          tableFilter.value.toggleRowSelection(row, true)
        })
    })
    loading.value = false
    return Promise.resolve(tableState.data)
  }
})

// data
// 页面加载状态
const loading = ref(false)
const tableFilter = ref(null)
// 表格对象
const tableState = reactive({
  data: {
    total: 0,
    rows: [],
    pageNum: 1,
    pageSize: 10
  },
  column: [
    {
      type: 'selection'
    },
    {
      label: '#',
      type: 'index',
      width: '50px'
    },
    {
      label: '销售',
      minWidth: '220px',
      slotName: 'sellerSlot'
    },
    {
      prop: 'outId',
      label: '外部单号',
      minWidth: '120px',
      showOverflowTooltip: true
    },
    {
      prop: 'deliverCustomerName',
      label: '客户',
      minWidth: '160px',
      showOverflowTooltip: true
    },
    {
      prop: 'invoiceCustomerName',
      label: '项目名称',
      minWidth: '120px',
      showOverflowTooltip: true
    },
    {
      label: '日期',
      minWidth: '160px',
      slotName: 'dateSlot'
    },
    {
      prop: 'sellQuantity',
      label: '单据数量',
      minWidth: '120px',
      showOverflowTooltip: true
    },
    {
      label: '单据金额',
      minWidth: '120px',
      slotName: 'amountSlot'
    }
  ],
  selection: []
})
const authObjList = ref([])
// 项目下拉列表
const prjProps = reactive({
  query: 'prjName', // 查询字段
  label: 'prjName', // 显示字段
  value: 'id', // 值字段
  apiMethod: async (val) => {
    const res = await listPrj({ ...val })
    return Promise.resolve({ data: { list: res.rows, total: res.total } })
  },
  single: null
})

// computed
const dialogVisible = computed({
  get() {
    return props.modelValue?.visible
  },
  set(val) {
    emit('update:modelValue', { visible: val })
  }
})

onMounted(() => {
  authObjList.value = props.modelValue.selected || []
})

// methods
// 提交
const onSubmit = () => {
  selectionSave()
  if (!authObjList.value.length) {
    useModal.msgWarning('请选择数据')
    return
  }
  emit('submitCallBack', authObjList.value)
  onCancel()
}
// 取消
const onCancel = () => {
  dialogVisible.value = false
}
const selectionSave = () => {
  const ids = (tableState.data['rows'] || []).map((v) => v.id)
  // 去除当前页的所有选项，再追加上当前页选中项
  authObjList.value = authObjList.value.filter((v) => ids.indexOf(v.id) === -1).concat(tableState.selection)
}
</script>