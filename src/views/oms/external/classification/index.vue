<template>
  <div class="app-container">
    <div class="common-filter-wrap">
      <el-form class="common-filter common-filter-clearfix">
        <el-form-item label="项目" prop="prjId">
          <SelectWaterfall
            v-model="searchState.searchForm.prjId"
            :props="prjProps"
            placeholder="请选择项目"
            class="bfb"
          />
        </el-form-item>
        <el-form-item label="外部类目名称" prop="cateName">
          <el-input v-model="searchState.searchForm.cateName" placeholder="请输入外部类目名称" clearable />
        </el-form-item>
        <el-form-item label="外部类目ID" prop="outId">
          <el-input v-model="searchState.searchForm.outId" placeholder="请输入外部类目编码" clearable />
        </el-form-item>
        <el-form-item label="外部类目父ID" prop="outPid">
          <el-input v-model="searchState.searchForm.outPid" placeholder="请输入外部类目父ID" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchState.searchForm.status" placeholder="请选择状态" clearable class="bfb">
            <el-option
              v-for="dict in sys_oms_manage_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="filter-btns">
          <el-button type="primary" @click="search">筛 选</el-button>
          <el-button @click="reset">重 置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="common-btn-wrap">
      <div class="left-btns">
        <el-button type="primary" @click="handleAdd" v-hasPermi="['oms:externalClassification:add']">添加外部类目
        </el-button>
        <el-button type="primary" v-hasPermi="['oms:prjOutCategory:add']" @click="handleImport">批量导入外部分类</el-button>
        <el-button type="primary" v-hasPermi="['oms:prjOutCategory:export']" @click="handleExportTemplate">导出模板</el-button>
      </div>
      <div class="right-btns">
        <el-button @click="handleExport" v-hasPermi="['oms:externalClassification:export']">导 出</el-button>
      </div>
    </div>
    <div class="basis-table">
      <TableFilter
        ref="tableFilter"
        row-key="id"
        data-key="rows"
        height="100%"
        :loading="loading"
        :column="tableState.column"
        :data="tableState.data"
        @pagination-change="paginationChange"
      >
        <template #statusSlot="{ row }">
          <dict-tag :options="sys_oms_manage_status" :value="row.status" />
        </template>
        <template #ctrSlot="{ row }">
          <el-link
            type="primary"
            :underline="false"
            class="basis-link"
            v-hasPermi="['oms:externalClassification:edit']"
            @click="handleUpdate(row)"
          >修改
          </el-link>
          <el-link
            type="primary"
            :underline="false"
            class="basis-link"
            v-hasPermi="['oms:externalClassification:remove']"
            @click="handleDelete(row)"
          >删除
          </el-link>
          <el-link
            type="primary"
            :underline="false"
            class="basis-link"
            v-hasPermi="['oms:externalClassification:status']"
            @click="handleStatus(row)"
          >{{ row.status === 'NOR' ? '停用' : '启用' }}
          </el-link>
        </template>
      </TableFilter>
    </div>
    <!--添加或修改项目外部类目弹窗-->
    <FormDialog v-if="formDialog.visible" v-model="formDialog" @reset-table="searchQuery" />
    <!--导入弹窗-->
    <AsyncImportDialog
      v-if="asyncImportDialog.visible"
      v-model="asyncImportDialog"
      :if-prj="asyncImportDialog.ifPrj"
      @reset-table="searchQuery"
    />
  </div>
</template>
<script name="OmsExternalClassification" setup>
// import
import useSearch from '@/utils/search'
import useDict from '@/utils/dict'
import useModal from '@/utils/modal'
import { download, downloadGet } from '@/utils/request'
import { delPrjOutCategory, listPrjOutCategory, updateStatus } from '@/api/oms/external/prjOutCategory'
import { listPrj } from '@/api/oms/prj/prj'
import TableFilter from '@/components/TableFilter'
import FormDialog from './components/FormDialog.vue'
import SelectWaterfall from '@/components/SelectWaterfall/index.vue'
import AsyncImportDialog from '@/views/components/AsyncImportDialog.vue'

// 枚举字典
const { sys_oms_manage_status } = useDict('sys_oms_manage_status')
// 查询对象
const { searchState, paginationChange, search, searchQuery, reset } = useSearch({
  searchForm: {
    prjId: undefined,
    outId: undefined,
    outPid: undefined,
    cateName: undefined,
    status: undefined
  },
  getData: async (val) => {
    loading.value = true
    const res = await listPrjOutCategory({
      ...val,
      pageNum: searchState.pageNum,
      pageSize: searchState.pageSize
    })
    Object.assign(tableState.data, { pageNum: searchState.pageNum, pageSize: searchState.pageSize, ...res })
    loading.value = false
    return Promise.resolve(tableState.data)
  }
})

// data
const loading = ref(false)
// 表格对象
const tableState = reactive({
  data: {
    total: 0,
    rows: [],
    pageNum: 1,
    pageSize: 10
  },
  column: [
    {
      prop: 'cateName',
      label: '外部类目名称',
      minWidth: '140px',
      showOverflowTooltip: true
    },
    {
      prop: 'outId',
      label: '外部类目ID',
      minWidth: '140px',
      showOverflowTooltip: true
    },
    {
      prop: 'prjName',
      label: '项目名称',
      minWidth: '140px',
      showOverflowTooltip: true
    },
    {
      prop: 'prjId',
      label: '项目ID',
      minWidth: '140px',
      showOverflowTooltip: true
    },
    {
      prop: 'backCateNames',
      label: '关联的采购类目',
      minWidth: '140px',
      showOverflowTooltip: true
    },
    {
      label: '状态',
      minWidth: '100px',
      slotName: 'statusSlot'
    },
    {
      label: '操作',
      width: '110px',
      fixed: 'right',
      align: 'right',
      slotName: 'ctrSlot'
    }
  ]
})
// 项目下拉列表
const prjProps = reactive({
  query: 'prjName', // 查询字段
  label: 'prjName', // 显示字段
  value: 'id', // 值字段
  apiMethod: async (val) => {
    const res = await listPrj({ ...val })
    return Promise.resolve({ data: { list: res.rows, total: res.total } })
  },
  single: null
})
// 表单弹窗
const formDialog = ref({ id: '', visible: false })
// 导入弹窗
const asyncImportDialog = ref({ importType: '', visible: false, ifPrj: false })

// methods
// 新增
const handleAdd = () => {
  Object.assign(formDialog.value, { id: '', visible: true })
}
// 修改
const handleUpdate = (row) => {
  Object.assign(formDialog.value, { id: row.id, visible: true })
}
// 删除
const handleDelete = (row) => {
  useModal
    .confirm('是否确认删除选中的数据项？')
    .then(function() {
      return delPrjOutCategory(row.id)
    })
    .then(() => {
      searchQuery()
      useModal.msgSuccess('删除成功')
    })
    .catch(() => {
    })
}
// 停用启用
const handleStatus = (row) => {
  let text = row.status !== 'NOR' ? '启用' : '停用'
  useModal
    .confirm('是否确认' + text + '选中的数据项')
    .then(() => {
      return updateStatus({ id: row.id, status: row.status === 'NOR' ? 'DIS' : 'NOR' })
    })
    .then(() => {
      useModal.msgSuccess(text + '成功')
      row.status = row.status === 'NOR' ? 'DIS' : 'NOR'
    })
    .catch(() => {
    })
}
// 导出
const handleExport = () => {
  download('oms/prjOutCategory/export', { ...searchState.searchForm }, `外部分类数据_${ new Date().getTime() }.xlsx`)
}
const handleImport = () => {
  Object.assign(asyncImportDialog.value, { importType: 'GOODS_PRJ_OUT_CATEGORY_IMPORT', visible: true, ifPrj:false })
}
// 导出模板
const handleExportTemplate = () => {
  downloadGet('/oms/prjOutCategory/exportTemplate', {}, `prj_out_category_template_${ new Date().getTime() }.xlsx`)
}
</script>
