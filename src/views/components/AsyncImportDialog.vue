<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    class="pb4"
    title="导入"
    :close-on-click-modal="false"
    :before-close="onCancel"
  >
    <el-form v-loading="loading" :model="formState" :rules="rulesState" ref="ruleFormRef" label-width="100px">
      <el-form-item label="项目" prop="prjId" v-if="props.ifPrj">
        <SelectWaterfall
          v-model="formState.prjId"
          v-model:data-list="prjList"
          :props="prjProps"
          placeholder="请选择项目"
          class="bfb"
        />
      </el-form-item>
      <el-form-item label="文件上传" prop="files">
        <FileUpload v-model="formState.files" accept=".xls,.xlsx" :maxSize="1" maxDisType="hidden" class="bfb-x" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
// import
import SelectWaterfall from '@/components/SelectWaterfall/index.vue'
import FileUpload from '@/views/components/FileUpload.vue'
import { listPrj } from '@/api/oms/prj/prj'
import { asyncImport } from '@/api/system/oss'
import useModal from '@/utils/modal'

const props = defineProps({
  modelValue: {
    type: Object,
    require: true,
    default() {
      return {
        importType: '',
        visible: false,
      }
    }
  },
  // 是否显示项目
  ifPrj: {
    type: Boolean,
    default() {
      return true
    }
  }
})
const emit = defineEmits(['resetTable', 'update:modelValue'])

// data
// 页面加载状态
const loading = ref(false)
const ruleFormRef = ref(null)
// 表单数据
const formState = ref({
  prjId: undefined,
  systemApp: 'OMS',
  files: []
})
const rulesState = reactive({
  prjId: [{ required: true, message: '请选择项目' }],
  files: [{ required: true, message: '请上传文件' }]
})
// 项目下拉列表
const prjProps = reactive({
  query: 'prjName', // 查询字段
  label: 'prjName', // 显示字段
  value: 'id', // 值字段
  apiMethod: async (val) => {
    const res = await listPrj({ ...val })
    return Promise.resolve({ data: { list: res.rows, total: res.total } })
  },
  single: null
})
const prjList = ref([])

// computed
const dialogVisible = computed({
  get() {
    return props.modelValue?.visible
  },
  set(val) {
    emit('update:modelValue', { visible: val })
  }
})

// methods
// 提交
const onSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const formData = {
        ...formState.value,
        importType: props.modelValue.importType,
        importFile: formState.value.files[0].url,
        files: undefined
      }
      formData.prjName = (prjList.value.find((v) => v.id === formData.prjId) || {})['prjName'] || undefined
      asyncImport(formData).then((res) => {
        emit('resetTable')
        useModal.msgSuccess('导入成功，请稍后在导入记录中查看结果')
        onCancel()
      })
    }
  })
}
// 取消
const onCancel = () => {
  dialogVisible.value = false
}
</script>