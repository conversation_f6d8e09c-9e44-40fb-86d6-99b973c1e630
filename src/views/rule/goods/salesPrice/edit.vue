<template>
  <div class="app-container">
    <el-form
      ref="ruleFormRef"
      v-loading="loading"
      :model="formState"
      :rules="rulesState"
      label-width="240px"
      label-position="right"
    >
      <el-form-item prop="ruleCode" label="规则编码">
        <el-input v-model="formState.ruleCode" placeholder="请输入规则编码" maxlength="50" class="per80" disabled />
      </el-form-item>
      <el-form-item prop="priority" label="优先级">
        <el-input-number
          v-model="formState.priority"
          placeholder="请输入优先级"
          controls-position="right"
          class="per80 tl"
          :min="1"
          :max="9999"
        />
      </el-form-item>
      <el-form-item prop="ruleName" label="规则名称">
        <el-input v-model="formState.ruleName" placeholder="请输入规则名称" maxlength="50" class="per80" />
      </el-form-item>
      <el-form-item prop="overall" label="全局规则">
        <el-radio-group v-model="formState.overall" :disabled="formState.id">
          <el-radio v-for="dict in sys_int_yes_no" :key="dict.value" :label="dict.value" :value="dict.value"
            >{{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="isIndefinite" label="有效期">
        <el-radio-group v-model="formState.isIndefinite">
          <el-radio label="0" value="0">有限</el-radio>
          <el-radio label="1" value="1">无限</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formState.isIndefinite === '0'" prop="validityDate" label="有效期时间">
        <div class="w350">
          <el-date-picker
            v-model="formState.validityDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </div>
      </el-form-item>
      <el-form-item prop="remark" label="备注">
        <el-input
          v-model="formState.remark"
          type="textarea"
          placeholder="请输入备注"
          class="per80"
          :rows="4"
          :maxlength="200"
          show-word-limit
        />
      </el-form-item>
      <template v-for="(fixedCon, idx) in formState.fixedConList" :key="idx">
        <FormItemInput
          v-if="!(+formState.overall === 1 && fixedCon.dimCode === 'dimOperatorId')"
          :prop="'fixedConList.' + idx + '.dimValue'"
          v-model="formState.fixedConList[idx]"
          valueKey="dimValue"
          item-class="per80"
        />
      </template>
      <template v-for="(result, idx) in formState.resultList" :key="idx">
        <FormItemInput
          :prop="'resultList.' + idx + '.returnVal'"
          v-model="formState.resultList[idx]"
          valueKey="returnVal"
          item-class="per80"
        />
      </template>
      <el-form-item prop="trendsConList" label="匹配条件">
        <div class="per80">
          <div class="basis-form-title dspf-jcsb">
            <el-button type="primary" size="small" plain @click="addTrendsConItem" :disabled="disabledPrj"
              >新增
            </el-button>
          </div>
          <div class="basis-form-table mt16">
            <el-table :data="formState.trendsConList" class="mb16">
              <el-table-column label="变量名" min-width="140">
                <template #default="scope">
                  <el-form-item
                    :prop="'trendsConList.' + scope.$index + '.dimCode'"
                    :rules="rulesState.dimCode"
                    label=""
                    label-width="0"
                  >
                    <el-select
                      v-model="scope.row['dimCode']"
                      placeholder="请选择"
                      class="bfb"
                      @change="changeTrendsConItem(scope.$index)"
                      :disabled="disabledPrj"
                    >
                      <el-option
                        v-for="item in matchingConditionList"
                        :key="item.dimCode"
                        :label="item.dimName"
                        :value="item.dimCode"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="140">
                <template #default="scope">
                  <el-form-item
                    :prop="'trendsConList.' + scope.$index + '.operator'"
                    :rules="rulesState.operator"
                    label=""
                    label-width="0"
                  >
                    <el-select v-model="scope.row['operator']" placeholder="请选择" class="bfb" :disabled="disabledPrj">
                      <el-option
                        v-for="item in operatorMap[scope.row['dimCode']] || []"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="条件值" min-width="140">
                <template #default="scope">
                  <FormItemInput
                    :prop="'trendsConList.' + scope.$index + '.dimValue'"
                    :prj-id="fixedPrj?.dimKey"
                    v-model="formState.trendsConList[scope.$index]"
                    valueKey="dimValue"
                    label=""
                    label-width="0"
                    item-class="bfb"
                    :disabled="disabledPrj"
                  />
                </template>
              </el-table-column>
              <el-table-column fixed="right" align="center" label="操作" width="80">
                <template #default="scope">
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="removeTrendsConItem(scope.$index)"
                    >删除
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form-item>
      <div class="basis-form-button">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">提交</el-button>
      </div>
    </el-form>
  </div>
</template>
<script name="RuleGoodsSalesPriceEdit" setup>
// import
import useModal from '@/utils/modal'
import useTab from '@/utils/tab'
import useDict from '@/utils/dict'
import { getLine, queryMatchingCondition, queryRuleCreate, saveRule } from '@/api/rule/line'
import FormItemInput from '@/views/rule/components/FormItemInput.vue'

const route = useRoute()
const router = useRouter()

// 枚举字典
const { sys_int_yes_no } = useDict('sys_int_yes_no')

// data
// 页面加载状态
const loading = ref(false)
const ruleFormRef = ref(null)
// 表单数据
const formState = ref({
  priority: undefined,
  ruleName: undefined,
  overall: '0',
  isIndefinite: '1',
  validityDate: [],
  remark: undefined,
  fixedConList: [],
  resultList: [],
  trendsConList: []
})
const rulesState = reactive({
  priority: [{ required: true, message: '请输入优先级' }],
  ruleName: [{ required: true, message: '请输入规则名称' }],
  overall: [{ required: true, message: '请选择全局规则' }],
  isIndefinite: [{ required: true, message: '请选择是否无限期' }],
  validityDate: [{ required: true, message: '请选择有效期时间' }],
  dimCode: [{ required: true, message: '请选择变量名' }],
  operator: [{ required: true, message: '请选择运算符' }]
})
const matchingConditionList = ref([])
const operatorMap = ref({})

// computed
const fixedPrj = computed(() => {
  return formState.value.fixedConList.find((item) => item.dimType === 9 && item.dimCode === 'dimOperatorId')
})
const disabledPrj = computed(() => {
  if (+formState.value.overall === 1) {
    return false
  } else {
    return !(fixedPrj.value && fixedPrj.value.dimValue)
  }
})

onMounted(() => {
  queryMatchingCondition('item_sale_price', '0').then((res) => {
    matchingConditionList.value = res.data
    operatorMap.value = res.data.reduce((acc, cur) => {
      acc[cur.dimCode] = cur.operatorList
      return acc
    }, {})
    if (route.query.id) {
      loadData(route.query.id)
    } else {
      queryRuleCreate('item_sale_price').then((res) => {
        Object.assign(
          formState.value,
          { ...res.data },
          {
            priority: undefined,
            ruleName: undefined,
            overall: '0',
            isIndefinite: '1',
            validityDate: [],
            trendsConList: []
          }
        )
      })
    }
  })
})

// methods
const onSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const formData = {
        ...formState.value,
        fixedConList: +formState.value.overall === 1 ? formState.value.fixedConList.filter(v => v.dimCode !== 'dimOperatorId')
          : formState.value.fixedConList,
        validityStartAt: formState.value.validityDate.length === 0 ? undefined : formState.value.validityDate[0],
        validityEndAt: formState.value.validityDate.length === 0 ? undefined : formState.value.validityDate[1],
        validityDate: undefined
      }
      saveRule(formData).then((res) => {
        useModal.msgSuccess('保存成功')
        useTab(router).removeCache('RuleGoodsSalesPrice')
        useTab(router).closeOpenPage('/rule/goods/sales-price')
      })
    }
  })
}
// 取消
const onCancel = () => {
  useTab(router).closeOpenPage('/rule/goods/sales-price')
}
// 回显加载数据
const loadData = (id) => {
  loading.value = true
  formState.value.id = id
  getLine(id).then(({ data }) => {
    Object.assign(formState.value, {
      ...data,
      isIndefinite: data.isIndefinite ? '1' : '0',
      overall: data.overall ? '1' : '0',
      validityDate: +data.isIndefinite === 0 ? [data.validityStartAt, data.validityEndAt] : []
    })
    loading.value = false
  })
}
// 添加匹配条件
const addTrendsConItem = () => {
  formState.value.trendsConList.push({
    dimName: '',
    dimType: 0,
    dimCode: undefined,
    operator: undefined,
    dimValue: null
  })
}
const changeTrendsConItem = (index) => {
  const item = matchingConditionList.value.find((item) => item.dimCode === formState.value.trendsConList[index].dimCode)
  if (item) {
    if (formState.value.trendsConList.filter((v) => v.dimCode === item.dimCode).length > 1) {
      useModal.msgError('请选择不同的变量名')
      formState.value.trendsConList[index].dimCode = undefined
      return
    }
    formState.value.trendsConList[index] = { ...item, operator: (item.operatorList && item.operatorList['0']) || '' }
  }
}
// 删除匹配条件
const removeTrendsConItem = (index) => {
  formState.value.trendsConList.splice(index, 1)
}
</script>
<style scoped lang="scss">
.basis-form-button {
  justify-content: start;
  padding-left: 212px;
}
</style>